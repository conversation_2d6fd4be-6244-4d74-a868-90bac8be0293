import { createClient } from '@supabase/supabase-js'

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL!
const supabaseAnonKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!
const supabaseServiceKey = process.env.SUPABASE_SERVICE_KEY!

// Client-side Supabase client
export const supabase = createClient(supabaseUrl, supabaseAnonKey)

// Server-side Supabase client with service key for admin operations
export const supabaseAdmin = createClient(supabaseUrl, supabaseServiceKey, {
  auth: {
    autoRefreshToken: false,
    persistSession: false
  }
})

// Database types
export interface WaitlistUser {
  id: string
  email: string
  name?: string
  company?: string
  role?: string
  interests?: string[]
  source: string
  utm_source?: string
  utm_medium?: string
  utm_campaign?: string
  referral_code: string
  referred_by?: string
  position: number
  status: 'waiting' | 'invited' | 'converted' | 'churned'
  ip_address?: string
  user_agent?: string
  country?: string
  city?: string
  metadata?: Record<string, any>
  created_at: string
  updated_at: string
}

export interface EngagementEvent {
  id: string
  user_id: string
  event_type: string
  event_data?: Record<string, any>
  session_id?: string
  page_url?: string
  referrer_url?: string
  ip_address?: string
  user_agent?: string
  created_at: string
}

export interface EmailEvent {
  id: string
  user_id: string
  email_type: string
  subject?: string
  sent_at: string
  opened_at?: string
  clicked_at?: string
  unsubscribed_at?: string
  bounced_at?: string
  metadata?: Record<string, any>
}