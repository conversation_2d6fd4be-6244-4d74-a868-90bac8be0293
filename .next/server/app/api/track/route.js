/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/track/route";
exports.ids = ["app/api/track/route"];
exports.modules = {

/***/ "(rsc)/./app/api/track/route.ts":
/*!********************************!*\
  !*** ./app/api/track/route.ts ***!
  \********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GET: () => (/* binding */ GET),\n/* harmony export */   POST: () => (/* binding */ POST)\n/* harmony export */ });\n/* harmony import */ var next_server__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/server */ \"(rsc)/./node_modules/next/dist/api/server.js\");\n/* harmony import */ var _lib_supabase__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/lib/supabase */ \"(rsc)/./lib/supabase.ts\");\n/* harmony import */ var next_headers__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/headers */ \"(rsc)/./node_modules/next/dist/api/headers.js\");\n\n\n\nasync function POST(request) {\n    try {\n        const body = await request.json();\n        const { userId, eventType, eventData = {}, sessionId, pageUrl, referrerUrl } = body;\n        // Get user IP and user agent\n        const headersList = (0,next_headers__WEBPACK_IMPORTED_MODULE_2__.headers)();\n        const ip_address = headersList.get('x-forwarded-for') || headersList.get('x-real-ip');\n        const user_agent = headersList.get('user-agent');\n        // If no userId, try to identify by email or session\n        let user_id = userId;\n        if (!user_id && eventData.email) {\n            const { data: user } = await _lib_supabase__WEBPACK_IMPORTED_MODULE_1__.supabaseAdmin.from('waitlist_users').select('id').eq('email', eventData.email).single();\n            if (user) {\n                user_id = user.id;\n            }\n        }\n        // Track the event\n        const { data: event, error } = await _lib_supabase__WEBPACK_IMPORTED_MODULE_1__.supabaseAdmin.rpc('track_engagement', {\n            p_user_id: user_id,\n            p_event_type: eventType,\n            p_event_data: eventData,\n            p_session_id: sessionId,\n            p_page_url: pageUrl || headersList.get('referer'),\n            p_ip_address: ip_address\n        });\n        if (error) {\n            console.error('Tracking error:', error);\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: 'Failed to track event'\n            }, {\n                status: 500\n            });\n        }\n        // Handle specific event types\n        switch(eventType){\n            case 'email_open':\n                await _lib_supabase__WEBPACK_IMPORTED_MODULE_1__.supabaseAdmin.from('email_events').update({\n                    opened_at: new Date().toISOString()\n                }).eq('id', eventData.emailId);\n                break;\n            case 'email_click':\n                await _lib_supabase__WEBPACK_IMPORTED_MODULE_1__.supabaseAdmin.from('email_events').update({\n                    clicked_at: new Date().toISOString()\n                }).eq('id', eventData.emailId);\n                break;\n            case 'referral_click':\n                // Track referral link clicks even for non-users\n                if (eventData.referralCode) {\n                    const { data: referrer } = await _lib_supabase__WEBPACK_IMPORTED_MODULE_1__.supabaseAdmin.from('waitlist_users').select('id').eq('referral_code', eventData.referralCode).single();\n                    if (referrer) {\n                        await _lib_supabase__WEBPACK_IMPORTED_MODULE_1__.supabaseAdmin.rpc('track_engagement', {\n                            p_user_id: referrer.id,\n                            p_event_type: 'referral_link_clicked',\n                            p_event_data: {\n                                ...eventData,\n                                clicked_by_ip: ip_address\n                            },\n                            p_ip_address: ip_address\n                        });\n                    }\n                }\n                break;\n        }\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: true,\n            eventId: event\n        });\n    } catch (error) {\n        console.error('Tracking error:', error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            error: 'Internal server error'\n        }, {\n            status: 500\n        });\n    }\n}\n// GET endpoint for email open tracking (using image pixel)\nasync function GET(request) {\n    const searchParams = request.nextUrl.searchParams;\n    const emailId = searchParams.get('eid');\n    const userId = searchParams.get('uid');\n    if (emailId) {\n        // Update email as opened\n        await _lib_supabase__WEBPACK_IMPORTED_MODULE_1__.supabaseAdmin.from('email_events').update({\n            opened_at: new Date().toISOString()\n        }).eq('id', emailId);\n        // Track engagement\n        if (userId) {\n            await _lib_supabase__WEBPACK_IMPORTED_MODULE_1__.supabaseAdmin.rpc('track_engagement', {\n                p_user_id: userId,\n                p_event_type: 'email_open',\n                p_event_data: {\n                    email_id: emailId\n                }\n            });\n        }\n    }\n    // Return 1x1 transparent pixel\n    const pixel = Buffer.from('iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNkYPhfDwAChwGA60e6kgAAAABJRU5ErkJggg==', 'base64');\n    return new next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse(pixel, {\n        headers: {\n            'Content-Type': 'image/png',\n            'Cache-Control': 'no-store, no-cache, must-revalidate, proxy-revalidate',\n            'Pragma': 'no-cache',\n            'Expires': '0'\n        }\n    });\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./app/api/track/route.ts\n");

/***/ }),

/***/ "(rsc)/./lib/supabase.ts":
/*!*************************!*\
  !*** ./lib/supabase.ts ***!
  \*************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   supabase: () => (/* binding */ supabase),\n/* harmony export */   supabaseAdmin: () => (/* binding */ supabaseAdmin)\n/* harmony export */ });\n/* harmony import */ var _supabase_supabase_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @supabase/supabase-js */ \"(rsc)/./node_modules/@supabase/supabase-js/dist/module/index.js\");\n\nconst supabaseUrl = \"https://uqohigtauhgygiyplftk.supabase.co\";\nconst supabaseAnonKey = \"eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InVxb2hpZ3RhdWhneWdpeXBsZnRrIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTE4ODc4MDEsImV4cCI6MjA2NzQ2MzgwMX0.qRR7Zy6ZqaxZUBuEt94QV7jEZLqbF2Fpc3ndUcJWP4s\";\nconst supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY;\n// Client-side Supabase client\nconst supabase = (0,_supabase_supabase_js__WEBPACK_IMPORTED_MODULE_0__.createClient)(supabaseUrl, supabaseAnonKey);\n// Server-side Supabase client with service key for admin operations\nconst supabaseAdmin = (0,_supabase_supabase_js__WEBPACK_IMPORTED_MODULE_0__.createClient)(supabaseUrl, supabaseServiceKey, {\n    auth: {\n        autoRefreshToken: false,\n        persistSession: false\n    }\n});\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./lib/supabase.ts\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Ftrack%2Froute&page=%2Fapi%2Ftrack%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Ftrack%2Froute.ts&appDir=%2FApplications%2FAI%20Project%20%2Fagentised%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FApplications%2FAI%20Project%20%2Fagentised&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!*******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Ftrack%2Froute&page=%2Fapi%2Ftrack%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Ftrack%2Froute.ts&appDir=%2FApplications%2FAI%20Project%20%2Fagentised%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FApplications%2FAI%20Project%20%2Fagentised&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \*******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   workAsyncStorage: () => (/* binding */ workAsyncStorage),\n/* harmony export */   workUnitAsyncStorage: () => (/* binding */ workUnitAsyncStorage)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(rsc)/./node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/./node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _Applications_AI_Project_agentised_app_api_track_route_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./app/api/track/route.ts */ \"(rsc)/./app/api/track/route.ts\");\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/track/route\",\n        pathname: \"/api/track\",\n        filename: \"route\",\n        bundlePath: \"app/api/track/route\"\n    },\n    resolvedPagePath: \"/Applications/AI Project /agentised/app/api/track/route.ts\",\n    nextConfigOutput,\n    userland: _Applications_AI_Project_agentised_app_api_track_route_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { workAsyncStorage, workUnitAsyncStorage, serverHooks } = routeModule;\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        workAsyncStorage,\n        workUnitAsyncStorage\n    });\n}\n\n\n//# sourceMappingURL=app-route.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Ftrack%2Froute&page=%2Fapi%2Ftrack%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Ftrack%2Froute.ts&appDir=%2FApplications%2FAI%20Project%20%2Fagentised%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FApplications%2FAI%20Project%20%2Fagentised&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "../app-render/after-task-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist/server/app-render/after-task-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/after-task-async-storage.external.js");

/***/ }),

/***/ "../app-render/work-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/server/app-render/work-async-storage.external.js" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-async-storage.external.js");

/***/ }),

/***/ "./work-unit-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist/server/app-render/work-unit-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-unit-async-storage.external.js");

/***/ }),

/***/ "?32c4":
/*!****************************!*\
  !*** bufferutil (ignored) ***!
  \****************************/
/***/ (() => {

/* (ignored) */

/***/ }),

/***/ "?66e9":
/*!********************************!*\
  !*** utf-8-validate (ignored) ***!
  \********************************/
/***/ (() => {

/* (ignored) */

/***/ }),

/***/ "buffer":
/*!*************************!*\
  !*** external "buffer" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("buffer");

/***/ }),

/***/ "crypto":
/*!*************************!*\
  !*** external "crypto" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("crypto");

/***/ }),

/***/ "events":
/*!*************************!*\
  !*** external "events" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("events");

/***/ }),

/***/ "http":
/*!***********************!*\
  !*** external "http" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("http");

/***/ }),

/***/ "https":
/*!************************!*\
  !*** external "https" ***!
  \************************/
/***/ ((module) => {

"use strict";
module.exports = require("https");

/***/ }),

/***/ "net":
/*!**********************!*\
  !*** external "net" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("net");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ }),

/***/ "punycode":
/*!***************************!*\
  !*** external "punycode" ***!
  \***************************/
/***/ ((module) => {

"use strict";
module.exports = require("punycode");

/***/ }),

/***/ "stream":
/*!*************************!*\
  !*** external "stream" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("stream");

/***/ }),

/***/ "tls":
/*!**********************!*\
  !*** external "tls" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("tls");

/***/ }),

/***/ "url":
/*!**********************!*\
  !*** external "url" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("url");

/***/ }),

/***/ "zlib":
/*!***********************!*\
  !*** external "zlib" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("zlib");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/@supabase","vendor-chunks/ws","vendor-chunks/whatwg-url","vendor-chunks/isows","vendor-chunks/tr46","vendor-chunks/webidl-conversions"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Ftrack%2Froute&page=%2Fapi%2Ftrack%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Ftrack%2Froute.ts&appDir=%2FApplications%2FAI%20Project%20%2Fagentised%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FApplications%2FAI%20Project%20%2Fagentised&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();