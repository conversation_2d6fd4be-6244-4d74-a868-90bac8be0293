/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["app/page"],{

/***/ "(app-pages-browser)/./components/WaitlistForm.tsx":
/*!*************************************!*\
  !*** ./components/WaitlistForm.tsx ***!
  \*************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ WaitlistForm)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _hooks_useWaitlist__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/hooks/useWaitlist */ \"(app-pages-browser)/./hooks/useWaitlist.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\nfunction WaitlistForm() {\n    _s();\n    const { joinWaitlist, loading, error, response } = (0,_hooks_useWaitlist__WEBPACK_IMPORTED_MODULE_2__.useWaitlist)();\n    const [formData, setFormData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        email: '',\n        name: '',\n        company: '',\n        role: '',\n        interests: []\n    });\n    const interestOptions = [\n        'API Access',\n        'Automation',\n        'Analytics',\n        'Integration',\n        'Custom Solutions'\n    ];\n    const handleSubmit = async (e)=>{\n        e.preventDefault();\n        try {\n            await joinWaitlist(formData);\n        } catch (err) {\n        // Error is already handled in the hook\n        }\n    };\n    const handleInterestToggle = (interest)=>{\n        setFormData((prev)=>({\n                ...prev,\n                interests: prev.interests.includes(interest) ? prev.interests.filter((i)=>i !== interest) : [\n                    ...prev.interests,\n                    interest\n                ]\n            }));\n    };\n    const copyReferralLink = ()=>{\n        if (response === null || response === void 0 ? void 0 : response.referral_link) {\n            navigator.clipboard.writeText(response.referral_link);\n            alert('Referral link copied!');\n        }\n    };\n    // Show success state\n    if (response === null || response === void 0 ? void 0 : response.success) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"max-w-md mx-auto p-6 bg-green-50 rounded-lg\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                    className: \"text-2xl font-bold text-green-800 mb-4\",\n                    children: \"\\uD83C\\uDF89 You're on the list!\"\n                }, void 0, false, {\n                    fileName: \"/Applications/AI Project /agentised/components/WaitlistForm.tsx\",\n                    lineNumber: 54,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                    className: \"text-gray-700 mb-4\",\n                    children: [\n                        \"You're currently \",\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                            children: [\n                                \"#\",\n                                response.position\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Applications/AI Project /agentised/components/WaitlistForm.tsx\",\n                            lineNumber: 58,\n                            columnNumber: 28\n                        }, this),\n                        \" on our waitlist.\"\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Applications/AI Project /agentised/components/WaitlistForm.tsx\",\n                    lineNumber: 57,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"bg-white p-4 rounded-md mb-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-sm text-gray-600 mb-2\",\n                            children: \"Move up the waitlist by referring friends:\"\n                        }, void 0, false, {\n                            fileName: \"/Applications/AI Project /agentised/components/WaitlistForm.tsx\",\n                            lineNumber: 62,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex gap-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                    type: \"text\",\n                                    value: response.referral_link || '',\n                                    readOnly: true,\n                                    className: \"flex-1 px-3 py-2 border rounded-md text-sm\"\n                                }, void 0, false, {\n                                    fileName: \"/Applications/AI Project /agentised/components/WaitlistForm.tsx\",\n                                    lineNumber: 66,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: copyReferralLink,\n                                    className: \"px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700\",\n                                    children: \"Copy\"\n                                }, void 0, false, {\n                                    fileName: \"/Applications/AI Project /agentised/components/WaitlistForm.tsx\",\n                                    lineNumber: 72,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Applications/AI Project /agentised/components/WaitlistForm.tsx\",\n                            lineNumber: 65,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-xs text-gray-500 mt-2\",\n                            children: \"Get 3 spots ahead for each friend who joins!\"\n                        }, void 0, false, {\n                            fileName: \"/Applications/AI Project /agentised/components/WaitlistForm.tsx\",\n                            lineNumber: 79,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Applications/AI Project /agentised/components/WaitlistForm.tsx\",\n                    lineNumber: 61,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"/Applications/AI Project /agentised/components/WaitlistForm.tsx\",\n            lineNumber: 53,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n        onSubmit: handleSubmit,\n        className: \"max-w-md mx-auto p-6\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                className: \"text-2xl font-bold mb-6\",\n                children: \"Join the Waitlist\"\n            }, void 0, false, {\n                fileName: \"/Applications/AI Project /agentised/components/WaitlistForm.tsx\",\n                lineNumber: 89,\n                columnNumber: 7\n            }, this),\n            error && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"mb-4 p-3 bg-red-50 text-red-700 rounded-md\",\n                children: error\n            }, void 0, false, {\n                fileName: \"/Applications/AI Project /agentised/components/WaitlistForm.tsx\",\n                lineNumber: 92,\n                columnNumber: 9\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"space-y-4\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                className: \"block text-sm font-medium mb-1\",\n                                children: \"Email *\"\n                            }, void 0, false, {\n                                fileName: \"/Applications/AI Project /agentised/components/WaitlistForm.tsx\",\n                                lineNumber: 99,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                type: \"email\",\n                                required: true,\n                                value: formData.email,\n                                onChange: (e)=>setFormData({\n                                        ...formData,\n                                        email: e.target.value\n                                    }),\n                                className: \"w-full px-3 py-2 border rounded-md\",\n                                placeholder: \"<EMAIL>\",\n                                \"data-track-click\": \"email_input\"\n                            }, void 0, false, {\n                                fileName: \"/Applications/AI Project /agentised/components/WaitlistForm.tsx\",\n                                lineNumber: 102,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Applications/AI Project /agentised/components/WaitlistForm.tsx\",\n                        lineNumber: 98,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                className: \"block text-sm font-medium mb-1\",\n                                children: \"Name\"\n                            }, void 0, false, {\n                                fileName: \"/Applications/AI Project /agentised/components/WaitlistForm.tsx\",\n                                lineNumber: 114,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                type: \"text\",\n                                value: formData.name,\n                                onChange: (e)=>setFormData({\n                                        ...formData,\n                                        name: e.target.value\n                                    }),\n                                className: \"w-full px-3 py-2 border rounded-md\",\n                                placeholder: \"John Doe\"\n                            }, void 0, false, {\n                                fileName: \"/Applications/AI Project /agentised/components/WaitlistForm.tsx\",\n                                lineNumber: 117,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Applications/AI Project /agentised/components/WaitlistForm.tsx\",\n                        lineNumber: 113,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                className: \"block text-sm font-medium mb-1\",\n                                children: \"Company\"\n                            }, void 0, false, {\n                                fileName: \"/Applications/AI Project /agentised/components/WaitlistForm.tsx\",\n                                lineNumber: 127,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                type: \"text\",\n                                value: formData.company,\n                                onChange: (e)=>setFormData({\n                                        ...formData,\n                                        company: e.target.value\n                                    }),\n                                className: \"w-full px-3 py-2 border rounded-md\",\n                                placeholder: \"Acme Inc\"\n                            }, void 0, false, {\n                                fileName: \"/Applications/AI Project /agentised/components/WaitlistForm.tsx\",\n                                lineNumber: 130,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Applications/AI Project /agentised/components/WaitlistForm.tsx\",\n                        lineNumber: 126,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                className: \"block text-sm font-medium mb-1\",\n                                children: \"Role\"\n                            }, void 0, false, {\n                                fileName: \"/Applications/AI Project /agentised/components/WaitlistForm.tsx\",\n                                lineNumber: 140,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                value: formData.role,\n                                onChange: (e)=>setFormData({\n                                        ...formData,\n                                        role: e.target.value\n                                    }),\n                                className: \"w-full px-3 py-2 border rounded-md\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                        value: \"\",\n                                        children: \"Select your role\"\n                                    }, void 0, false, {\n                                        fileName: \"/Applications/AI Project /agentised/components/WaitlistForm.tsx\",\n                                        lineNumber: 148,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                        value: \"founder\",\n                                        children: \"Founder/CEO\"\n                                    }, void 0, false, {\n                                        fileName: \"/Applications/AI Project /agentised/components/WaitlistForm.tsx\",\n                                        lineNumber: 149,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                        value: \"developer\",\n                                        children: \"Developer\"\n                                    }, void 0, false, {\n                                        fileName: \"/Applications/AI Project /agentised/components/WaitlistForm.tsx\",\n                                        lineNumber: 150,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                        value: \"designer\",\n                                        children: \"Designer\"\n                                    }, void 0, false, {\n                                        fileName: \"/Applications/AI Project /agentised/components/WaitlistForm.tsx\",\n                                        lineNumber: 151,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                        value: \"marketer\",\n                                        children: \"Marketer\"\n                                    }, void 0, false, {\n                                        fileName: \"/Applications/AI Project /agentised/components/WaitlistForm.tsx\",\n                                        lineNumber: 152,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                        value: \"other\",\n                                        children: \"Other\"\n                                    }, void 0, false, {\n                                        fileName: \"/Applications/AI Project /agentised/components/WaitlistForm.tsx\",\n                                        lineNumber: 153,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Applications/AI Project /agentised/components/WaitlistForm.tsx\",\n                                lineNumber: 143,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Applications/AI Project /agentised/components/WaitlistForm.tsx\",\n                        lineNumber: 139,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                className: \"block text-sm font-medium mb-2\",\n                                children: \"What are you interested in?\"\n                            }, void 0, false, {\n                                fileName: \"/Applications/AI Project /agentised/components/WaitlistForm.tsx\",\n                                lineNumber: 158,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"space-y-2\",\n                                children: interestOptions.map((interest)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                        className: \"flex items-center\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                type: \"checkbox\",\n                                                checked: formData.interests.includes(interest),\n                                                onChange: ()=>handleInterestToggle(interest),\n                                                className: \"mr-2\"\n                                            }, void 0, false, {\n                                                fileName: \"/Applications/AI Project /agentised/components/WaitlistForm.tsx\",\n                                                lineNumber: 164,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-sm\",\n                                                children: interest\n                                            }, void 0, false, {\n                                                fileName: \"/Applications/AI Project /agentised/components/WaitlistForm.tsx\",\n                                                lineNumber: 170,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, interest, true, {\n                                        fileName: \"/Applications/AI Project /agentised/components/WaitlistForm.tsx\",\n                                        lineNumber: 163,\n                                        columnNumber: 15\n                                    }, this))\n                            }, void 0, false, {\n                                fileName: \"/Applications/AI Project /agentised/components/WaitlistForm.tsx\",\n                                lineNumber: 161,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Applications/AI Project /agentised/components/WaitlistForm.tsx\",\n                        lineNumber: 157,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        type: \"submit\",\n                        disabled: loading,\n                        className: \"w-full py-3 bg-blue-600 text-white rounded-md hover:bg-blue-700 disabled:bg-blue-300\",\n                        \"data-track-click\": \"submit_waitlist\",\n                        children: loading ? 'Joining...' : 'Join Waitlist'\n                    }, void 0, false, {\n                        fileName: \"/Applications/AI Project /agentised/components/WaitlistForm.tsx\",\n                        lineNumber: 176,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Applications/AI Project /agentised/components/WaitlistForm.tsx\",\n                lineNumber: 97,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/Applications/AI Project /agentised/components/WaitlistForm.tsx\",\n        lineNumber: 88,\n        columnNumber: 5\n    }, this);\n}\n_s(WaitlistForm, \"jqva0Eo+1VwbfqCWKOvHVzJEPdk=\", false, function() {\n    return [\n        _hooks_useWaitlist__WEBPACK_IMPORTED_MODULE_2__.useWaitlist\n    ];\n});\n_c = WaitlistForm;\nvar _c;\n$RefreshReg$(_c, \"WaitlistForm\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./components/WaitlistForm.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./hooks/useWaitlist.ts":
/*!******************************!*\
  !*** ./hooks/useWaitlist.ts ***!
  \******************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useEngagementTracking: () => (/* binding */ useEngagementTracking),\n/* harmony export */   useWaitlist: () => (/* binding */ useWaitlist)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _lib_tracking__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/lib/tracking */ \"(app-pages-browser)/./lib/tracking.ts\");\n\n\nfunction useWaitlist() {\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(false);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(null);\n    const [response, setResponse] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(null);\n    // Initialize tracking on mount\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)({\n        \"useWaitlist.useEffect\": ()=>{\n            // Check for referral code in URL\n            const urlParams = new URLSearchParams(window.location.search);\n            const refCode = urlParams.get('ref');\n            if (refCode) {\n                // Track referral click\n                (0,_lib_tracking__WEBPACK_IMPORTED_MODULE_1__.trackEvent)('referral_click', {\n                    referralCode: refCode\n                });\n                // Store in session for later use\n                sessionStorage.setItem('referral_code', refCode);\n            }\n            // Initialize page tracking\n            (0,_lib_tracking__WEBPACK_IMPORTED_MODULE_1__.initializeTracking)();\n        }\n    }[\"useWaitlist.useEffect\"], []);\n    const joinWaitlist = async (data)=>{\n        setLoading(true);\n        setError(null);\n        // Track form start\n        (0,_lib_tracking__WEBPACK_IMPORTED_MODULE_1__.trackFormInteraction)('waitlist_form', 'started');\n        try {\n            // Get referral code from session if exists\n            const referralCode = sessionStorage.getItem('referral_code');\n            // Get UTM parameters\n            const urlParams = new URLSearchParams(window.location.search);\n            const utmData = {\n                utm_source: data.utm_source || urlParams.get('utm_source') || undefined,\n                utm_medium: data.utm_medium || urlParams.get('utm_medium') || undefined,\n                utm_campaign: data.utm_campaign || urlParams.get('utm_campaign') || undefined\n            };\n            const response = await fetch('/api/waitlist/join', {\n                method: 'POST',\n                headers: {\n                    'Content-Type': 'application/json'\n                },\n                body: JSON.stringify({\n                    ...data,\n                    ...utmData,\n                    referralCode,\n                    source: window.location.hostname === 'localhost' ? 'development' : 'website'\n                })\n            });\n            const result = await response.json();\n            if (!response.ok) {\n                throw new Error(result.error || 'Failed to join waitlist');\n            }\n            setResponse(result);\n            // Track successful signup\n            (0,_lib_tracking__WEBPACK_IMPORTED_MODULE_1__.trackFormInteraction)('waitlist_form', 'completed');\n            (0,_lib_tracking__WEBPACK_IMPORTED_MODULE_1__.trackEvent)('waitlist_success', {\n                position: result.position,\n                referral_code: result.referral_code\n            });\n            // Clear referral code from session after use\n            sessionStorage.removeItem('referral_code');\n            return result;\n        } catch (err) {\n            const errorMessage = err instanceof Error ? err.message : 'Something went wrong';\n            setError(errorMessage);\n            // Track form abandonment\n            (0,_lib_tracking__WEBPACK_IMPORTED_MODULE_1__.trackFormInteraction)('waitlist_form', 'abandoned');\n            (0,_lib_tracking__WEBPACK_IMPORTED_MODULE_1__.trackEvent)('waitlist_error', {\n                error: errorMessage\n            });\n            throw err;\n        } finally{\n            setLoading(false);\n        }\n    };\n    const checkPosition = async (email)=>{\n        try {\n            const response = await fetch('/api/waitlist/check', {\n                method: 'POST',\n                headers: {\n                    'Content-Type': 'application/json'\n                },\n                body: JSON.stringify({\n                    email\n                })\n            });\n            const result = await response.json();\n            if (!response.ok) {\n                throw new Error(result.error || 'Failed to check position');\n            }\n            (0,_lib_tracking__WEBPACK_IMPORTED_MODULE_1__.trackEvent)('position_check', {\n                email\n            });\n            return result;\n        } catch (err) {\n            const errorMessage = err instanceof Error ? err.message : 'Something went wrong';\n            setError(errorMessage);\n            throw err;\n        }\n    };\n    return {\n        joinWaitlist,\n        checkPosition,\n        loading,\n        error,\n        response\n    };\n}\n// Hook for tracking user behavior\nfunction useEngagementTracking(userId) {\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)({\n        \"useEngagementTracking.useEffect\": ()=>{\n            if (!userId) return;\n            // Track user-specific events\n            const trackUserEvent = {\n                \"useEngagementTracking.useEffect.trackUserEvent\": (eventType, eventData)=>{\n                    (0,_lib_tracking__WEBPACK_IMPORTED_MODULE_1__.trackEvent)(eventType, eventData, userId);\n                }\n            }[\"useEngagementTracking.useEffect.trackUserEvent\"];\n            // Track clicks on important elements\n            const handleClick = {\n                \"useEngagementTracking.useEffect.handleClick\": (e)=>{\n                    const target = e.target;\n                    // Track CTA clicks\n                    if (target.matches('[data-track-click]')) {\n                        const trackingData = target.getAttribute('data-track-click');\n                        trackUserEvent('cta_click', {\n                            element: trackingData\n                        });\n                    }\n                    // Track external link clicks\n                    if (target.matches('a[href^=\"http\"]')) {\n                        const href = target.getAttribute('href');\n                        trackUserEvent('external_link_click', {\n                            url: href\n                        });\n                    }\n                }\n            }[\"useEngagementTracking.useEffect.handleClick\"];\n            document.addEventListener('click', handleClick);\n            return ({\n                \"useEngagementTracking.useEffect\": ()=>{\n                    document.removeEventListener('click', handleClick);\n                }\n            })[\"useEngagementTracking.useEffect\"];\n        }\n    }[\"useEngagementTracking.useEffect\"], [\n        userId\n    ]);\n}\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./hooks/useWaitlist.ts\n"));

/***/ }),

/***/ "(app-pages-browser)/./lib/tracking.ts":
/*!*************************!*\
  !*** ./lib/tracking.ts ***!
  \*************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   getEmailTrackingPixel: () => (/* binding */ getEmailTrackingPixel),\n/* harmony export */   getSessionId: () => (/* binding */ getSessionId),\n/* harmony export */   getTrackedLink: () => (/* binding */ getTrackedLink),\n/* harmony export */   initializeTracking: () => (/* binding */ initializeTracking),\n/* harmony export */   trackEvent: () => (/* binding */ trackEvent),\n/* harmony export */   trackFormInteraction: () => (/* binding */ trackFormInteraction),\n/* harmony export */   trackPageView: () => (/* binding */ trackPageView),\n/* harmony export */   trackReferralClick: () => (/* binding */ trackReferralClick)\n/* harmony export */ });\n/* harmony import */ var uuid__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! uuid */ \"(app-pages-browser)/./node_modules/uuid/dist/esm-browser/v4.js\");\n\n// Get or create session ID\nfunction getSessionId() {\n    if (false) {}\n    let sessionId = sessionStorage.getItem('waitlist_session_id');\n    if (!sessionId) {\n        sessionId = (0,uuid__WEBPACK_IMPORTED_MODULE_0__[\"default\"])();\n        sessionStorage.setItem('waitlist_session_id', sessionId);\n    }\n    return sessionId;\n}\n// Track engagement events\nasync function trackEvent(eventType) {\n    let eventData = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : {}, userId = arguments.length > 2 ? arguments[2] : void 0;\n    try {\n        const sessionId = getSessionId();\n        await fetch('/api/track', {\n            method: 'POST',\n            headers: {\n                'Content-Type': 'application/json'\n            },\n            body: JSON.stringify({\n                userId,\n                eventType,\n                eventData,\n                sessionId,\n                pageUrl: window.location.href,\n                referrerUrl: document.referrer\n            })\n        });\n    } catch (error) {\n        console.error('Failed to track event:', error);\n    }\n}\n// Track page views\nfunction trackPageView(userId) {\n    trackEvent('page_view', {\n        path: window.location.pathname,\n        search: window.location.search,\n        hash: window.location.hash\n    }, userId);\n}\n// Track form interactions\nfunction trackFormInteraction(formName, action, userId) {\n    trackEvent('form_interaction', {\n        form_name: formName,\n        action,\n        timestamp: new Date().toISOString()\n    }, userId);\n}\n// Track referral clicks\nfunction trackReferralClick(referralCode) {\n    trackEvent('referral_click', {\n        referralCode,\n        landing_page: window.location.pathname\n    });\n}\n// Initialize tracking on page load\nfunction initializeTracking(userId) {\n    // Track page view\n    trackPageView(userId);\n    // Track time on page\n    let startTime = Date.now();\n    window.addEventListener('beforeunload', ()=>{\n        const timeOnPage = Math.round((Date.now() - startTime) / 1000);\n        trackEvent('page_exit', {\n            time_on_page: timeOnPage,\n            path: window.location.pathname\n        }, userId);\n    });\n    // Track scroll depth\n    let maxScroll = 0;\n    let ticking = false;\n    function updateMaxScroll() {\n        const scrollPercent = Math.round(window.scrollY / (document.documentElement.scrollHeight - window.innerHeight) * 100);\n        if (scrollPercent > maxScroll) {\n            maxScroll = scrollPercent;\n            if (maxScroll % 25 === 0) {\n                trackEvent('scroll_depth', {\n                    depth: maxScroll,\n                    path: window.location.pathname\n                }, userId);\n            }\n        }\n        ticking = false;\n    }\n    window.addEventListener('scroll', ()=>{\n        if (!ticking) {\n            window.requestAnimationFrame(updateMaxScroll);\n            ticking = true;\n        }\n    });\n}\n// Email tracking helpers\nfunction getEmailTrackingPixel(emailId, userId) {\n    return \"\".concat(\"http://localhost:3000\", \"/api/track?eid=\").concat(emailId, \"&uid=\").concat(userId);\n}\nfunction getTrackedLink(url, emailId, linkId) {\n    const trackingUrl = new URL(\"\".concat(\"http://localhost:3000\", \"/api/track/click\"));\n    trackingUrl.searchParams.set('url', url);\n    trackingUrl.searchParams.set('eid', emailId);\n    trackingUrl.searchParams.set('lid', linkId);\n    return trackingUrl.toString();\n}\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./lib/tracking.ts\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FApplications%2FAI%20Project%20%2Fagentised%2Fcomponents%2FWaitlistForm.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&server=false!":
/*!***********************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FApplications%2FAI%20Project%20%2Fagentised%2Fcomponents%2FWaitlistForm.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&server=false! ***!
  \***********************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval(__webpack_require__.ts("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./components/WaitlistForm.tsx */ \"(app-pages-browser)/./components/WaitlistForm.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9uZXh0L2Rpc3QvYnVpbGQvd2VicGFjay9sb2FkZXJzL25leHQtZmxpZ2h0LWNsaWVudC1lbnRyeS1sb2FkZXIuanM/bW9kdWxlcz0lN0IlMjJyZXF1ZXN0JTIyJTNBJTIyJTJGQXBwbGljYXRpb25zJTJGQUklMjBQcm9qZWN0JTIwJTJGYWdlbnRpc2VkJTJGY29tcG9uZW50cyUyRldhaXRsaXN0Rm9ybS50c3glMjIlMkMlMjJpZHMlMjIlM0ElNUIlMjJkZWZhdWx0JTIyJTVEJTdEJnNlcnZlcj1mYWxzZSEiLCJtYXBwaW5ncyI6IkFBQUEsb0xBQWlJIiwic291cmNlcyI6WyIiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIsIHdlYnBhY2tFeHBvcnRzOiBbXCJkZWZhdWx0XCJdICovIFwiL0FwcGxpY2F0aW9ucy9BSSBQcm9qZWN0IC9hZ2VudGlzZWQvY29tcG9uZW50cy9XYWl0bGlzdEZvcm0udHN4XCIpO1xuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FApplications%2FAI%20Project%20%2Fagentised%2Fcomponents%2FWaitlistForm.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&server=false!\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/next/dist/compiled/react/cjs/react-jsx-dev-runtime.development.js":
/*!****************************************************************************************!*\
  !*** ./node_modules/next/dist/compiled/react/cjs/react-jsx-dev-runtime.development.js ***!
  \****************************************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("/**\n * @license React\n * react-jsx-dev-runtime.development.js\n *\n * Copyright (c) Meta Platforms, Inc. and affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n */\n\n\n true &&\n  (function () {\n    function getComponentNameFromType(type) {\n      if (null == type) return null;\n      if (\"function\" === typeof type)\n        return type.$$typeof === REACT_CLIENT_REFERENCE\n          ? null\n          : type.displayName || type.name || null;\n      if (\"string\" === typeof type) return type;\n      switch (type) {\n        case REACT_FRAGMENT_TYPE:\n          return \"Fragment\";\n        case REACT_PROFILER_TYPE:\n          return \"Profiler\";\n        case REACT_STRICT_MODE_TYPE:\n          return \"StrictMode\";\n        case REACT_SUSPENSE_TYPE:\n          return \"Suspense\";\n        case REACT_SUSPENSE_LIST_TYPE:\n          return \"SuspenseList\";\n        case REACT_ACTIVITY_TYPE:\n          return \"Activity\";\n      }\n      if (\"object\" === typeof type)\n        switch (\n          (\"number\" === typeof type.tag &&\n            console.error(\n              \"Received an unexpected object in getComponentNameFromType(). This is likely a bug in React. Please file an issue.\"\n            ),\n          type.$$typeof)\n        ) {\n          case REACT_PORTAL_TYPE:\n            return \"Portal\";\n          case REACT_CONTEXT_TYPE:\n            return (type.displayName || \"Context\") + \".Provider\";\n          case REACT_CONSUMER_TYPE:\n            return (type._context.displayName || \"Context\") + \".Consumer\";\n          case REACT_FORWARD_REF_TYPE:\n            var innerType = type.render;\n            type = type.displayName;\n            type ||\n              ((type = innerType.displayName || innerType.name || \"\"),\n              (type = \"\" !== type ? \"ForwardRef(\" + type + \")\" : \"ForwardRef\"));\n            return type;\n          case REACT_MEMO_TYPE:\n            return (\n              (innerType = type.displayName || null),\n              null !== innerType\n                ? innerType\n                : getComponentNameFromType(type.type) || \"Memo\"\n            );\n          case REACT_LAZY_TYPE:\n            innerType = type._payload;\n            type = type._init;\n            try {\n              return getComponentNameFromType(type(innerType));\n            } catch (x) {}\n        }\n      return null;\n    }\n    function testStringCoercion(value) {\n      return \"\" + value;\n    }\n    function checkKeyStringCoercion(value) {\n      try {\n        testStringCoercion(value);\n        var JSCompiler_inline_result = !1;\n      } catch (e) {\n        JSCompiler_inline_result = !0;\n      }\n      if (JSCompiler_inline_result) {\n        JSCompiler_inline_result = console;\n        var JSCompiler_temp_const = JSCompiler_inline_result.error;\n        var JSCompiler_inline_result$jscomp$0 =\n          (\"function\" === typeof Symbol &&\n            Symbol.toStringTag &&\n            value[Symbol.toStringTag]) ||\n          value.constructor.name ||\n          \"Object\";\n        JSCompiler_temp_const.call(\n          JSCompiler_inline_result,\n          \"The provided key is an unsupported type %s. This value must be coerced to a string before using it here.\",\n          JSCompiler_inline_result$jscomp$0\n        );\n        return testStringCoercion(value);\n      }\n    }\n    function getTaskName(type) {\n      if (type === REACT_FRAGMENT_TYPE) return \"<>\";\n      if (\n        \"object\" === typeof type &&\n        null !== type &&\n        type.$$typeof === REACT_LAZY_TYPE\n      )\n        return \"<...>\";\n      try {\n        var name = getComponentNameFromType(type);\n        return name ? \"<\" + name + \">\" : \"<...>\";\n      } catch (x) {\n        return \"<...>\";\n      }\n    }\n    function getOwner() {\n      var dispatcher = ReactSharedInternals.A;\n      return null === dispatcher ? null : dispatcher.getOwner();\n    }\n    function UnknownOwner() {\n      return Error(\"react-stack-top-frame\");\n    }\n    function hasValidKey(config) {\n      if (hasOwnProperty.call(config, \"key\")) {\n        var getter = Object.getOwnPropertyDescriptor(config, \"key\").get;\n        if (getter && getter.isReactWarning) return !1;\n      }\n      return void 0 !== config.key;\n    }\n    function defineKeyPropWarningGetter(props, displayName) {\n      function warnAboutAccessingKey() {\n        specialPropKeyWarningShown ||\n          ((specialPropKeyWarningShown = !0),\n          console.error(\n            \"%s: `key` is not a prop. Trying to access it will result in `undefined` being returned. If you need to access the same value within the child component, you should pass it as a different prop. (https://react.dev/link/special-props)\",\n            displayName\n          ));\n      }\n      warnAboutAccessingKey.isReactWarning = !0;\n      Object.defineProperty(props, \"key\", {\n        get: warnAboutAccessingKey,\n        configurable: !0\n      });\n    }\n    function elementRefGetterWithDeprecationWarning() {\n      var componentName = getComponentNameFromType(this.type);\n      didWarnAboutElementRef[componentName] ||\n        ((didWarnAboutElementRef[componentName] = !0),\n        console.error(\n          \"Accessing element.ref was removed in React 19. ref is now a regular prop. It will be removed from the JSX Element type in a future release.\"\n        ));\n      componentName = this.props.ref;\n      return void 0 !== componentName ? componentName : null;\n    }\n    function ReactElement(\n      type,\n      key,\n      self,\n      source,\n      owner,\n      props,\n      debugStack,\n      debugTask\n    ) {\n      self = props.ref;\n      type = {\n        $$typeof: REACT_ELEMENT_TYPE,\n        type: type,\n        key: key,\n        props: props,\n        _owner: owner\n      };\n      null !== (void 0 !== self ? self : null)\n        ? Object.defineProperty(type, \"ref\", {\n            enumerable: !1,\n            get: elementRefGetterWithDeprecationWarning\n          })\n        : Object.defineProperty(type, \"ref\", { enumerable: !1, value: null });\n      type._store = {};\n      Object.defineProperty(type._store, \"validated\", {\n        configurable: !1,\n        enumerable: !1,\n        writable: !0,\n        value: 0\n      });\n      Object.defineProperty(type, \"_debugInfo\", {\n        configurable: !1,\n        enumerable: !1,\n        writable: !0,\n        value: null\n      });\n      Object.defineProperty(type, \"_debugStack\", {\n        configurable: !1,\n        enumerable: !1,\n        writable: !0,\n        value: debugStack\n      });\n      Object.defineProperty(type, \"_debugTask\", {\n        configurable: !1,\n        enumerable: !1,\n        writable: !0,\n        value: debugTask\n      });\n      Object.freeze && (Object.freeze(type.props), Object.freeze(type));\n      return type;\n    }\n    function jsxDEVImpl(\n      type,\n      config,\n      maybeKey,\n      isStaticChildren,\n      source,\n      self,\n      debugStack,\n      debugTask\n    ) {\n      var children = config.children;\n      if (void 0 !== children)\n        if (isStaticChildren)\n          if (isArrayImpl(children)) {\n            for (\n              isStaticChildren = 0;\n              isStaticChildren < children.length;\n              isStaticChildren++\n            )\n              validateChildKeys(children[isStaticChildren]);\n            Object.freeze && Object.freeze(children);\n          } else\n            console.error(\n              \"React.jsx: Static children should always be an array. You are likely explicitly calling React.jsxs or React.jsxDEV. Use the Babel transform instead.\"\n            );\n        else validateChildKeys(children);\n      if (hasOwnProperty.call(config, \"key\")) {\n        children = getComponentNameFromType(type);\n        var keys = Object.keys(config).filter(function (k) {\n          return \"key\" !== k;\n        });\n        isStaticChildren =\n          0 < keys.length\n            ? \"{key: someKey, \" + keys.join(\": ..., \") + \": ...}\"\n            : \"{key: someKey}\";\n        didWarnAboutKeySpread[children + isStaticChildren] ||\n          ((keys =\n            0 < keys.length ? \"{\" + keys.join(\": ..., \") + \": ...}\" : \"{}\"),\n          console.error(\n            'A props object containing a \"key\" prop is being spread into JSX:\\n  let props = %s;\\n  <%s {...props} />\\nReact keys must be passed directly to JSX without using spread:\\n  let props = %s;\\n  <%s key={someKey} {...props} />',\n            isStaticChildren,\n            children,\n            keys,\n            children\n          ),\n          (didWarnAboutKeySpread[children + isStaticChildren] = !0));\n      }\n      children = null;\n      void 0 !== maybeKey &&\n        (checkKeyStringCoercion(maybeKey), (children = \"\" + maybeKey));\n      hasValidKey(config) &&\n        (checkKeyStringCoercion(config.key), (children = \"\" + config.key));\n      if (\"key\" in config) {\n        maybeKey = {};\n        for (var propName in config)\n          \"key\" !== propName && (maybeKey[propName] = config[propName]);\n      } else maybeKey = config;\n      children &&\n        defineKeyPropWarningGetter(\n          maybeKey,\n          \"function\" === typeof type\n            ? type.displayName || type.name || \"Unknown\"\n            : type\n        );\n      return ReactElement(\n        type,\n        children,\n        self,\n        source,\n        getOwner(),\n        maybeKey,\n        debugStack,\n        debugTask\n      );\n    }\n    function validateChildKeys(node) {\n      \"object\" === typeof node &&\n        null !== node &&\n        node.$$typeof === REACT_ELEMENT_TYPE &&\n        node._store &&\n        (node._store.validated = 1);\n    }\n    var React = __webpack_require__(/*! next/dist/compiled/react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\"),\n      REACT_ELEMENT_TYPE = Symbol.for(\"react.transitional.element\"),\n      REACT_PORTAL_TYPE = Symbol.for(\"react.portal\"),\n      REACT_FRAGMENT_TYPE = Symbol.for(\"react.fragment\"),\n      REACT_STRICT_MODE_TYPE = Symbol.for(\"react.strict_mode\"),\n      REACT_PROFILER_TYPE = Symbol.for(\"react.profiler\");\n    Symbol.for(\"react.provider\");\n    var REACT_CONSUMER_TYPE = Symbol.for(\"react.consumer\"),\n      REACT_CONTEXT_TYPE = Symbol.for(\"react.context\"),\n      REACT_FORWARD_REF_TYPE = Symbol.for(\"react.forward_ref\"),\n      REACT_SUSPENSE_TYPE = Symbol.for(\"react.suspense\"),\n      REACT_SUSPENSE_LIST_TYPE = Symbol.for(\"react.suspense_list\"),\n      REACT_MEMO_TYPE = Symbol.for(\"react.memo\"),\n      REACT_LAZY_TYPE = Symbol.for(\"react.lazy\"),\n      REACT_ACTIVITY_TYPE = Symbol.for(\"react.activity\"),\n      REACT_CLIENT_REFERENCE = Symbol.for(\"react.client.reference\"),\n      ReactSharedInternals =\n        React.__CLIENT_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE,\n      hasOwnProperty = Object.prototype.hasOwnProperty,\n      isArrayImpl = Array.isArray,\n      createTask = console.createTask\n        ? console.createTask\n        : function () {\n            return null;\n          };\n    React = {\n      \"react-stack-bottom-frame\": function (callStackForError) {\n        return callStackForError();\n      }\n    };\n    var specialPropKeyWarningShown;\n    var didWarnAboutElementRef = {};\n    var unknownOwnerDebugStack = React[\"react-stack-bottom-frame\"].bind(\n      React,\n      UnknownOwner\n    )();\n    var unknownOwnerDebugTask = createTask(getTaskName(UnknownOwner));\n    var didWarnAboutKeySpread = {};\n    exports.Fragment = REACT_FRAGMENT_TYPE;\n    exports.jsxDEV = function (\n      type,\n      config,\n      maybeKey,\n      isStaticChildren,\n      source,\n      self\n    ) {\n      var trackActualOwner =\n        1e4 > ReactSharedInternals.recentlyCreatedOwnerStacks++;\n      return jsxDEVImpl(\n        type,\n        config,\n        maybeKey,\n        isStaticChildren,\n        source,\n        self,\n        trackActualOwner\n          ? Error(\"react-stack-top-frame\")\n          : unknownOwnerDebugStack,\n        trackActualOwner ? createTask(getTaskName(type)) : unknownOwnerDebugTask\n      );\n    };\n  })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/compiled/react/cjs/react-jsx-dev-runtime.development.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js":
/*!******************************************************************!*\
  !*** ./node_modules/next/dist/compiled/react/jsx-dev-runtime.js ***!
  \******************************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("\n\nif (false) {} else {\n  module.exports = __webpack_require__(/*! ./cjs/react-jsx-dev-runtime.development.js */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/cjs/react-jsx-dev-runtime.development.js\");\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9uZXh0L2Rpc3QvY29tcGlsZWQvcmVhY3QvanN4LWRldi1ydW50aW1lLmpzIiwibWFwcGluZ3MiOiJBQUFhOztBQUViLElBQUksS0FBcUMsRUFBRSxFQUUxQyxDQUFDO0FBQ0YsRUFBRSw4TEFBc0U7QUFDeEUiLCJzb3VyY2VzIjpbIi9BcHBsaWNhdGlvbnMvQUkgUHJvamVjdCAvYWdlbnRpc2VkL25vZGVfbW9kdWxlcy9uZXh0L2Rpc3QvY29tcGlsZWQvcmVhY3QvanN4LWRldi1ydW50aW1lLmpzIl0sInNvdXJjZXNDb250ZW50IjpbIid1c2Ugc3RyaWN0JztcblxuaWYgKHByb2Nlc3MuZW52Lk5PREVfRU5WID09PSAncHJvZHVjdGlvbicpIHtcbiAgbW9kdWxlLmV4cG9ydHMgPSByZXF1aXJlKCcuL2Nqcy9yZWFjdC1qc3gtZGV2LXJ1bnRpbWUucHJvZHVjdGlvbi5qcycpO1xufSBlbHNlIHtcbiAgbW9kdWxlLmV4cG9ydHMgPSByZXF1aXJlKCcuL2Nqcy9yZWFjdC1qc3gtZGV2LXJ1bnRpbWUuZGV2ZWxvcG1lbnQuanMnKTtcbn1cbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/uuid/dist/esm-browser/native.js":
/*!******************************************************!*\
  !*** ./node_modules/uuid/dist/esm-browser/native.js ***!
  \******************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\nconst randomUUID = typeof crypto !== 'undefined' && crypto.randomUUID && crypto.randomUUID.bind(crypto);\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ({ randomUUID });\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy91dWlkL2Rpc3QvZXNtLWJyb3dzZXIvbmF0aXZlLmpzIiwibWFwcGluZ3MiOiI7Ozs7QUFBQTtBQUNBLGlFQUFlLEVBQUUsWUFBWSxFQUFDIiwic291cmNlcyI6WyIvQXBwbGljYXRpb25zL0FJIFByb2plY3QgL2FnZW50aXNlZC9ub2RlX21vZHVsZXMvdXVpZC9kaXN0L2VzbS1icm93c2VyL25hdGl2ZS5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJjb25zdCByYW5kb21VVUlEID0gdHlwZW9mIGNyeXB0byAhPT0gJ3VuZGVmaW5lZCcgJiYgY3J5cHRvLnJhbmRvbVVVSUQgJiYgY3J5cHRvLnJhbmRvbVVVSUQuYmluZChjcnlwdG8pO1xuZXhwb3J0IGRlZmF1bHQgeyByYW5kb21VVUlEIH07XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/uuid/dist/esm-browser/native.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/uuid/dist/esm-browser/regex.js":
/*!*****************************************************!*\
  !*** ./node_modules/uuid/dist/esm-browser/regex.js ***!
  \*****************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (/^(?:[0-9a-f]{8}-[0-9a-f]{4}-[1-8][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}|00000000-0000-0000-0000-000000000000|ffffffff-ffff-ffff-ffff-ffffffffffff)$/i);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy91dWlkL2Rpc3QvZXNtLWJyb3dzZXIvcmVnZXguanMiLCJtYXBwaW5ncyI6Ijs7OztBQUFBLGlFQUFlLGNBQWMsRUFBRSxVQUFVLEVBQUUsZUFBZSxFQUFFLGdCQUFnQixFQUFFLFVBQVUsR0FBRyw4RUFBOEUsRUFBQyIsInNvdXJjZXMiOlsiL0FwcGxpY2F0aW9ucy9BSSBQcm9qZWN0IC9hZ2VudGlzZWQvbm9kZV9tb2R1bGVzL3V1aWQvZGlzdC9lc20tYnJvd3Nlci9yZWdleC5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJleHBvcnQgZGVmYXVsdCAvXig/OlswLTlhLWZdezh9LVswLTlhLWZdezR9LVsxLThdWzAtOWEtZl17M30tWzg5YWJdWzAtOWEtZl17M30tWzAtOWEtZl17MTJ9fDAwMDAwMDAwLTAwMDAtMDAwMC0wMDAwLTAwMDAwMDAwMDAwMHxmZmZmZmZmZi1mZmZmLWZmZmYtZmZmZi1mZmZmZmZmZmZmZmYpJC9pO1xuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/uuid/dist/esm-browser/regex.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/uuid/dist/esm-browser/rng.js":
/*!***************************************************!*\
  !*** ./node_modules/uuid/dist/esm-browser/rng.js ***!
  \***************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ rng)\n/* harmony export */ });\nlet getRandomValues;\nconst rnds8 = new Uint8Array(16);\nfunction rng() {\n    if (!getRandomValues) {\n        if (typeof crypto === 'undefined' || !crypto.getRandomValues) {\n            throw new Error('crypto.getRandomValues() not supported. See https://github.com/uuidjs/uuid#getrandomvalues-not-supported');\n        }\n        getRandomValues = crypto.getRandomValues.bind(crypto);\n    }\n    return getRandomValues(rnds8);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy91dWlkL2Rpc3QvZXNtLWJyb3dzZXIvcm5nLmpzIiwibWFwcGluZ3MiOiI7Ozs7QUFBQTtBQUNBO0FBQ2U7QUFDZjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBIiwic291cmNlcyI6WyIvQXBwbGljYXRpb25zL0FJIFByb2plY3QgL2FnZW50aXNlZC9ub2RlX21vZHVsZXMvdXVpZC9kaXN0L2VzbS1icm93c2VyL3JuZy5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJsZXQgZ2V0UmFuZG9tVmFsdWVzO1xuY29uc3Qgcm5kczggPSBuZXcgVWludDhBcnJheSgxNik7XG5leHBvcnQgZGVmYXVsdCBmdW5jdGlvbiBybmcoKSB7XG4gICAgaWYgKCFnZXRSYW5kb21WYWx1ZXMpIHtcbiAgICAgICAgaWYgKHR5cGVvZiBjcnlwdG8gPT09ICd1bmRlZmluZWQnIHx8ICFjcnlwdG8uZ2V0UmFuZG9tVmFsdWVzKSB7XG4gICAgICAgICAgICB0aHJvdyBuZXcgRXJyb3IoJ2NyeXB0by5nZXRSYW5kb21WYWx1ZXMoKSBub3Qgc3VwcG9ydGVkLiBTZWUgaHR0cHM6Ly9naXRodWIuY29tL3V1aWRqcy91dWlkI2dldHJhbmRvbXZhbHVlcy1ub3Qtc3VwcG9ydGVkJyk7XG4gICAgICAgIH1cbiAgICAgICAgZ2V0UmFuZG9tVmFsdWVzID0gY3J5cHRvLmdldFJhbmRvbVZhbHVlcy5iaW5kKGNyeXB0byk7XG4gICAgfVxuICAgIHJldHVybiBnZXRSYW5kb21WYWx1ZXMocm5kczgpO1xufVxuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/uuid/dist/esm-browser/rng.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/uuid/dist/esm-browser/stringify.js":
/*!*********************************************************!*\
  !*** ./node_modules/uuid/dist/esm-browser/stringify.js ***!
  \*********************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__),\n/* harmony export */   unsafeStringify: () => (/* binding */ unsafeStringify)\n/* harmony export */ });\n/* harmony import */ var _validate_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./validate.js */ \"(app-pages-browser)/./node_modules/uuid/dist/esm-browser/validate.js\");\n\nconst byteToHex = [];\nfor (let i = 0; i < 256; ++i) {\n    byteToHex.push((i + 0x100).toString(16).slice(1));\n}\nfunction unsafeStringify(arr, offset = 0) {\n    return (byteToHex[arr[offset + 0]] +\n        byteToHex[arr[offset + 1]] +\n        byteToHex[arr[offset + 2]] +\n        byteToHex[arr[offset + 3]] +\n        '-' +\n        byteToHex[arr[offset + 4]] +\n        byteToHex[arr[offset + 5]] +\n        '-' +\n        byteToHex[arr[offset + 6]] +\n        byteToHex[arr[offset + 7]] +\n        '-' +\n        byteToHex[arr[offset + 8]] +\n        byteToHex[arr[offset + 9]] +\n        '-' +\n        byteToHex[arr[offset + 10]] +\n        byteToHex[arr[offset + 11]] +\n        byteToHex[arr[offset + 12]] +\n        byteToHex[arr[offset + 13]] +\n        byteToHex[arr[offset + 14]] +\n        byteToHex[arr[offset + 15]]).toLowerCase();\n}\nfunction stringify(arr, offset = 0) {\n    const uuid = unsafeStringify(arr, offset);\n    if (!(0,_validate_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(uuid)) {\n        throw TypeError('Stringified UUID is invalid');\n    }\n    return uuid;\n}\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (stringify);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/uuid/dist/esm-browser/stringify.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/uuid/dist/esm-browser/v4.js":
/*!**************************************************!*\
  !*** ./node_modules/uuid/dist/esm-browser/v4.js ***!
  \**************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var _native_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./native.js */ \"(app-pages-browser)/./node_modules/uuid/dist/esm-browser/native.js\");\n/* harmony import */ var _rng_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./rng.js */ \"(app-pages-browser)/./node_modules/uuid/dist/esm-browser/rng.js\");\n/* harmony import */ var _stringify_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./stringify.js */ \"(app-pages-browser)/./node_modules/uuid/dist/esm-browser/stringify.js\");\n\n\n\nfunction v4(options, buf, offset) {\n    if (_native_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"].randomUUID && !buf && !options) {\n        return _native_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"].randomUUID();\n    }\n    options = options || {};\n    const rnds = options.random ?? options.rng?.() ?? (0,_rng_js__WEBPACK_IMPORTED_MODULE_1__[\"default\"])();\n    if (rnds.length < 16) {\n        throw new Error('Random bytes length must be >= 16');\n    }\n    rnds[6] = (rnds[6] & 0x0f) | 0x40;\n    rnds[8] = (rnds[8] & 0x3f) | 0x80;\n    if (buf) {\n        offset = offset || 0;\n        if (offset < 0 || offset + 16 > buf.length) {\n            throw new RangeError(`UUID byte range ${offset}:${offset + 15} is out of buffer bounds`);\n        }\n        for (let i = 0; i < 16; ++i) {\n            buf[offset + i] = rnds[i];\n        }\n        return buf;\n    }\n    return (0,_stringify_js__WEBPACK_IMPORTED_MODULE_2__.unsafeStringify)(rnds);\n}\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (v4);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy91dWlkL2Rpc3QvZXNtLWJyb3dzZXIvdjQuanMiLCJtYXBwaW5ncyI6Ijs7Ozs7OztBQUFpQztBQUNOO0FBQ3NCO0FBQ2pEO0FBQ0EsUUFBUSxrREFBTTtBQUNkLGVBQWUsa0RBQU07QUFDckI7QUFDQTtBQUNBLHNEQUFzRCxtREFBRztBQUN6RDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0Esb0RBQW9ELE9BQU8sR0FBRyxhQUFhO0FBQzNFO0FBQ0Esd0JBQXdCLFFBQVE7QUFDaEM7QUFDQTtBQUNBO0FBQ0E7QUFDQSxXQUFXLDhEQUFlO0FBQzFCO0FBQ0EsaUVBQWUsRUFBRSxFQUFDIiwic291cmNlcyI6WyIvQXBwbGljYXRpb25zL0FJIFByb2plY3QgL2FnZW50aXNlZC9ub2RlX21vZHVsZXMvdXVpZC9kaXN0L2VzbS1icm93c2VyL3Y0LmpzIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCBuYXRpdmUgZnJvbSAnLi9uYXRpdmUuanMnO1xuaW1wb3J0IHJuZyBmcm9tICcuL3JuZy5qcyc7XG5pbXBvcnQgeyB1bnNhZmVTdHJpbmdpZnkgfSBmcm9tICcuL3N0cmluZ2lmeS5qcyc7XG5mdW5jdGlvbiB2NChvcHRpb25zLCBidWYsIG9mZnNldCkge1xuICAgIGlmIChuYXRpdmUucmFuZG9tVVVJRCAmJiAhYnVmICYmICFvcHRpb25zKSB7XG4gICAgICAgIHJldHVybiBuYXRpdmUucmFuZG9tVVVJRCgpO1xuICAgIH1cbiAgICBvcHRpb25zID0gb3B0aW9ucyB8fCB7fTtcbiAgICBjb25zdCBybmRzID0gb3B0aW9ucy5yYW5kb20gPz8gb3B0aW9ucy5ybmc/LigpID8/IHJuZygpO1xuICAgIGlmIChybmRzLmxlbmd0aCA8IDE2KSB7XG4gICAgICAgIHRocm93IG5ldyBFcnJvcignUmFuZG9tIGJ5dGVzIGxlbmd0aCBtdXN0IGJlID49IDE2Jyk7XG4gICAgfVxuICAgIHJuZHNbNl0gPSAocm5kc1s2XSAmIDB4MGYpIHwgMHg0MDtcbiAgICBybmRzWzhdID0gKHJuZHNbOF0gJiAweDNmKSB8IDB4ODA7XG4gICAgaWYgKGJ1Zikge1xuICAgICAgICBvZmZzZXQgPSBvZmZzZXQgfHwgMDtcbiAgICAgICAgaWYgKG9mZnNldCA8IDAgfHwgb2Zmc2V0ICsgMTYgPiBidWYubGVuZ3RoKSB7XG4gICAgICAgICAgICB0aHJvdyBuZXcgUmFuZ2VFcnJvcihgVVVJRCBieXRlIHJhbmdlICR7b2Zmc2V0fToke29mZnNldCArIDE1fSBpcyBvdXQgb2YgYnVmZmVyIGJvdW5kc2ApO1xuICAgICAgICB9XG4gICAgICAgIGZvciAobGV0IGkgPSAwOyBpIDwgMTY7ICsraSkge1xuICAgICAgICAgICAgYnVmW29mZnNldCArIGldID0gcm5kc1tpXTtcbiAgICAgICAgfVxuICAgICAgICByZXR1cm4gYnVmO1xuICAgIH1cbiAgICByZXR1cm4gdW5zYWZlU3RyaW5naWZ5KHJuZHMpO1xufVxuZXhwb3J0IGRlZmF1bHQgdjQ7XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/uuid/dist/esm-browser/v4.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/uuid/dist/esm-browser/validate.js":
/*!********************************************************!*\
  !*** ./node_modules/uuid/dist/esm-browser/validate.js ***!
  \********************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var _regex_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./regex.js */ \"(app-pages-browser)/./node_modules/uuid/dist/esm-browser/regex.js\");\n\nfunction validate(uuid) {\n    return typeof uuid === 'string' && _regex_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"].test(uuid);\n}\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (validate);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy91dWlkL2Rpc3QvZXNtLWJyb3dzZXIvdmFsaWRhdGUuanMiLCJtYXBwaW5ncyI6Ijs7Ozs7QUFBK0I7QUFDL0I7QUFDQSx1Q0FBdUMsaURBQUs7QUFDNUM7QUFDQSxpRUFBZSxRQUFRLEVBQUMiLCJzb3VyY2VzIjpbIi9BcHBsaWNhdGlvbnMvQUkgUHJvamVjdCAvYWdlbnRpc2VkL25vZGVfbW9kdWxlcy91dWlkL2Rpc3QvZXNtLWJyb3dzZXIvdmFsaWRhdGUuanMiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IFJFR0VYIGZyb20gJy4vcmVnZXguanMnO1xuZnVuY3Rpb24gdmFsaWRhdGUodXVpZCkge1xuICAgIHJldHVybiB0eXBlb2YgdXVpZCA9PT0gJ3N0cmluZycgJiYgUkVHRVgudGVzdCh1dWlkKTtcbn1cbmV4cG9ydCBkZWZhdWx0IHZhbGlkYXRlO1xuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/uuid/dist/esm-browser/validate.js\n"));

/***/ })

},
/******/ __webpack_require__ => { // webpackRuntimeModules
/******/ var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
/******/ __webpack_require__.O(0, ["main-app"], () => (__webpack_exec__("(app-pages-browser)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FApplications%2FAI%20Project%20%2Fagentised%2Fcomponents%2FWaitlistForm.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&server=false!")));
/******/ var __webpack_exports__ = __webpack_require__.O();
/******/ _N_E = __webpack_exports__;
/******/ }
]);