import { NextResponse } from 'next/server'
import { createClient } from '@supabase/supabase-js'

export async function POST(request: Request) {
  try {
    const body = await request.json()
    const { 
      email, 
      name, 
      company, 
      role, 
      interests,
      source = 'website',
      utm_source,
      utm_medium,
      utm_campaign,
      referred_by_code,
      qr_code_data
    } = body

    // Validate email
    if (!email || !isValidEmail(email)) {
      return NextResponse.json(
        { error: 'Invalid email address' },
        { status: 400 }
      )
    }

    // Initialize Supabase client
    const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL
    const supabaseServiceKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY // Using anon key for now

    if (!supabaseUrl || !supabaseServiceKey) {
      console.error('Missing Supabase credentials')
      return NextResponse.json(
        { error: 'Server configuration error' },
        { status: 500 }
      )
    }

    const supabase = createClient(supabaseUrl, supabaseServiceKey)

    // Get IP and user agent
    const ip_address = request.headers.get('x-forwarded-for') || request.headers.get('x-real-ip') || null
    const user_agent = request.headers.get('user-agent') || null

    // Skip referral for now
    let referred_by = null

    // Build metadata
    const metadata: any = {}
    if (qr_code_data) {
      metadata.qr_code_data = qr_code_data
    }

    // Insert user
    const { data: user, error: insertError } = await supabase
      .from('waitlist_users')
      .insert({
        email,
        name,
        company,
        role,
        interests,
        source,
        utm_source,
        utm_medium,
        utm_campaign,
        referred_by,
        ip_address,
        user_agent,
        metadata: Object.keys(metadata).length > 0 ? metadata : undefined
      })
      .select()
      .single()

    if (insertError) {
      // Check if email already exists
      if (insertError.code === '23505') {
        return NextResponse.json(
          { error: 'Email already registered' },
          { status: 409 }
        )
      }
      console.error('Insert error:', insertError)
      return NextResponse.json(
        { error: insertError.message || 'Failed to join waitlist' },
        { status: 500 }
      )
    }

    // Skip tracking for now - these might be causing errors
    // We'll add them back once basic signup works

    return NextResponse.json({
      message: 'Successfully joined waitlist!',
      position: user.position,
      referral_code: user.referral_code
    }, { status: 200 })
  } catch (error) {
    console.error('Subscription error:', error)
    return NextResponse.json(
      { error: 'Failed to subscribe' },
      { status: 500 }
    )
  }
}

function isValidEmail(email: string): boolean {
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
  return emailRegex.test(email)
}

// Example function to add to mailing list
// async function addToMailingList(email: string) {
//   const response = await fetch('https://api.sendgrid.com/v3/contacts', {
//     method: 'PUT',
//     headers: {
//       'Authorization': `Bearer ${process.env.SENDGRID_API_KEY}`,
//       'Content-Type': 'application/json',
//     },
//     body: JSON.stringify({
//       contacts: [{ email }],
//       list_ids: [process.env.SENDGRID_LIST_ID],
//     }),
//   })
//   
//   if (!response.ok) {
//     throw new Error('Failed to add to mailing list')
//   }
// }