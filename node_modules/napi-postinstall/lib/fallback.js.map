{"version": 3, "file": "fallback.js", "sourceRoot": "", "sources": ["../src/fallback.ts"], "names": [], "mappings": ";AAAA,2DAAiD;AACjD,8BAA6B;AAC7B,8BAA6B;AAC7B,kCAAiC;AAEjC,iDAA4C;AAC5C,6CAAuE;AAGvE,MAAM,SAAS,GAAG;IAChB,GAAG,EAAE,KAAK;IACV,IAAI,EAAE,MAAM;IACZ,IAAI,EAAE,MAAM;IACZ,GAAG,EAAE,KAAK;IACV,IAAI,EAAE,CAAC,IAAc,EAAE,EAAE,CAAC,CAAC,MAAM,EAAE,KAAK,EAAE,OAAO,IAAI,CAAC,CAAC,CAAC,EAAE,EAAE,GAAG,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC;CAC9E,CAAA;AAED,SAAS,gBAAgB,CACvB,KAAyD,EACzD,IAAc;IAEd,MAAM,IAAI,GACR,OAAO,KAAK,KAAK,UAAU;QACzB,CAAC,CAAC,KAAK,CAAC,IAAI,CAAC;QACb,CAAC;YACE,EAAe,CAAC,MAAM,CAAC,KAAK,EAAE,IAAI,CAAC,CAAA;IAC1C,OAAO;QACL,OAAO,EAAE,IAAI,CAAC,CAAC,CAAC;QAChB,IAAI,EAAE,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC;KACpB,CAAA;AACH,CAAC;AASD,SAAS,QAAQ,CACf,eAAuB,EACvB,YAAsB;IAEtB,MAAM,WAAW,GAAG,OAAO,CAAC,eAAe,CAAgB,CAAA;IAE3D,MAAM,EAAE,IAAI,EAAE,OAAO,EAAE,UAAU,EAAE,oBAAoB,EAAE,GAAG,WAAW,CAAA;IAEvE,MAAM,EAAE,IAAI,EAAE,OAAO,GAAG,UAAU,EAAE,GAAG,IAAA,uCAA0B,EAC/D,WAAW,EACX,YAAY,CACb,CAAA;IAED,IAAI,YAAY,IAAI,UAAU,KAAK,OAAO,EAAE,CAAC;QAC3C,MAAM,IAAI,KAAK,CACb,IAAA,yBAAY,EACV,6CAA6C,IAAI,OAAO,UAAU,SAAS,IAAI,CAAC,WAAW,OAAO,OAAO,GAAG,CAC7G,CACF,CAAA;IACH,CAAC;IAED,IAAI,OAAO,CAAC,QAAQ,CAAC,YAAY,EAAE,CAAC;QAClC,MAAM,cAAc,GAAG,GAAG,IAAI,CAAC,WAAW,IAAI,0BAAW,EAAE,CAAA;QAE3D,IAAI,CAAC,oBAAoB,EAAE,CAAC,cAAc,CAAC,EAAE,CAAC;YAC5C,MAAM,IAAI,KAAK,CACb,IAAA,yBAAY,EACV,KAAK,0BAAW,kCAAkC,IAAI,OAAO,OAAO,EAAE,CACvE,CACF,CAAA;QACH,CAAC;QAED,MAAM,OAAO,GAAG,IAAI,CAAC,OAAO,CAAC,EAAE,CAAC,MAAM,EAAE,EAAE,GAAG,IAAI,IAAI,OAAO,EAAE,CAAC,CAAA;QAE/D,MAAM,YAAY,GAAG,IAAI,CAAC,OAAO,CAC/B,OAAO,EACP,gBAAgB,cAAc,IAAI,IAAI,CAAC,UAAU,WAAW,CAC7D,CAAA;QAED,IAAI,CAAC,EAAE,CAAC,UAAU,CAAC,YAAY,CAAC,EAAE,CAAC;YACjC,EAAE,CAAC,MAAM,CAAC,OAAO,EAAE,EAAE,SAAS,EAAE,IAAI,EAAE,KAAK,EAAE,IAAI,EAAE,CAAC,CAAA;YACpD,EAAE,CAAC,SAAS,CAAC,OAAO,EAAE,EAAE,SAAS,EAAE,IAAI,EAAE,CAAC,CAAA;YAE1C,MAAM,UAAU,GAAG,GAAG,cAAc,IAAI,OAAO,EAAE,CAAA;YAEjD,OAAO,CAAC,GAAG,CACT,IAAA,yBAAY,EAAC,iBAAiB,UAAU,uBAAuB,CAAC,CACjE,CAAA;YAED,IAAA,iCAAY,EAAC,MAAM,EAAE,CAAC,GAAG,EAAE,UAAU,CAAC,EAAE;gBACtC,GAAG,EAAE,OAAO;gBACZ,KAAK,EAAE,SAAS;aACjB,CAAC,CAAA;QACJ,CAAC;QAED,OAAO,OAAO,CAAC,YAAY,CAAM,CAAA;IACnC,CAAC;IAED,MAAM,SAAS,GAAG,CAAC,CAAC,OAAO,CAAC,GAAG,CAAC,qBAAqB,IAAI,EAAE,CAAC,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;QACxE,KAAK,CAA2B,CAAA;IAElC,MAAM,QAAQ,GAAG,SAAS,CAAC,SAAS,CAAC,CAAA;IAErC,IAAI,CAAC,QAAQ,EAAE,CAAC;QACd,MAAM,IAAI,KAAK,CACb,IAAA,yBAAY,EACV,gCAAgC,SAAS,6BAA6B,MAAM,CAAC,IAAI,CAC/E,SAAS,CACV,CAAC,IAAI,CAAC,IAAI,CAAC,GAAG,CAChB,CACF,CAAA;IACH,CAAC;IAED,MAAM,EAAE,OAAO,EAAE,IAAI,EAAE,GAAG,gBAAgB,CAAC,QAAQ,EAAE;QACnD,kBAAkB;QAClB,IAAI;QACJ,OAAO;QACP,YAAY,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,GAAG;KACzB,CAAC,CAAA;IAEF,MAAM,MAAM,GAAG,IAAI,CAAC,OAAO,CAAC,eAAe,CAAC,CAAA;IAE5C,IAAA,iCAAY,EAAC,OAAO,EAAE,IAAI,EAAE;QAC1B,GAAG,EAAE,MAAM;QACX,KAAK,EAAE,SAAS;KACjB,CAAC,CAAA;IAGF,OAAO,CAAC,GAAG,CAAC,QAAQ,IAAI,CAAC,OAAO,CAAC,IAAI,EAAE,GAAG,CAAC,CAAC,WAAW,EAAE,WAAW,CAAC,GAAG,GAAG,CAAA;IAE3E,MAAM,iBAAiB,GAAG,OAAO,CAAC,OAAO,CAAC,MAAM,CAAC,CAAA;IAEjD,OAAO,OAAO,CAAC,KAAK,CAAC,iBAAiB,CAAC,CAAA;IAEvC,OAAO,OAAO,CAAC,MAAM,CAAM,CAAA;AAC7B,CAAC;AAED,iBAAS,QAAQ,CAAA"}