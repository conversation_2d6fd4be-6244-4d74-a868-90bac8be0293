{"name": "@typescript-eslint/eslint-plugin", "version": "8.35.1", "description": "TypeScript plugin for ESLint", "files": ["dist", "!*.tsbuil<PERSON><PERSON>", "index.d.ts", "raw-plugin.d.ts", "rules.d.ts", "package.json", "./README.md", "LICENSE"], "type": "commonjs", "exports": {".": {"types": "./index.d.ts", "default": "./dist/index.js"}, "./package.json": "./package.json", "./use-at-your-own-risk/rules": {"types": "./rules.d.ts", "default": "./dist/rules/index.js"}, "./use-at-your-own-risk/raw-plugin": {"types": "./raw-plugin.d.ts", "default": "./dist/raw-plugin.js"}}, "engines": {"node": "^18.18.0 || ^20.9.0 || >=21.1.0"}, "repository": {"type": "git", "url": "https://github.com/typescript-eslint/typescript-eslint.git", "directory": "packages/eslint-plugin"}, "bugs": {"url": "https://github.com/typescript-eslint/typescript-eslint/issues"}, "homepage": "https://typescript-eslint.io/packages/eslint-plugin", "license": "MIT", "keywords": ["eslint", "eslintplugin", "eslint-plugin", "typescript"], "scripts": {"//": "These package scripts are mostly here for convenience. Task running is handled by Nx at the root level.", "build": "yarn run -BT nx build", "clean": "rimraf dist/ coverage/", "format": "yarn run -T format", "generate-breaking-changes": "yarn run -BT nx generate-breaking-changes", "generate-configs": "yarn run -T generate-configs", "lint": "yarn run -BT nx lint", "test": "yarn run -BT nx test", "typecheck": "yarn run -BT nx typecheck"}, "dependencies": {"@eslint-community/regexpp": "^4.10.0", "@typescript-eslint/scope-manager": "8.35.1", "@typescript-eslint/type-utils": "8.35.1", "@typescript-eslint/utils": "8.35.1", "@typescript-eslint/visitor-keys": "8.35.1", "graphemer": "^1.4.0", "ignore": "^7.0.0", "natural-compare": "^1.4.0", "ts-api-utils": "^2.1.0"}, "devDependencies": {"@types/mdast": "^4.0.3", "@types/natural-compare": "*", "@typescript-eslint/rule-schema-to-typescript-types": "8.35.1", "@typescript-eslint/rule-tester": "8.35.1", "@vitest/coverage-v8": "^3.1.3", "ajv": "^6.12.6", "cross-fetch": "*", "eslint": "*", "json-schema": "*", "markdown-table": "^3.0.3", "marked": "^15.0.0", "mdast-util-from-markdown": "^2.0.0", "mdast-util-mdx": "^3.0.0", "micromark-extension-mdxjs": "^3.0.0", "rimraf": "*", "title-case": "^4.0.0", "tsx": "*", "typescript": "*", "unist-util-visit": "^5.0.0", "vitest": "^3.1.3"}, "peerDependencies": {"@typescript-eslint/parser": "^8.35.1", "eslint": "^8.57.0 || ^9.0.0", "typescript": ">=4.8.4 <5.9.0"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/typescript-eslint"}, "nx": {"name": "eslint-plugin", "includedScripts": ["clean"], "targets": {"generate-breaking-changes": {"command": "tsx tools/generate-breaking-changes.mts", "options": {"cwd": "{projectRoot}"}, "dependsOn": ["type-utils:build"]}}}}